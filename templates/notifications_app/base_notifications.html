{% extends 'base.html' %}

{% block extra_css %}
    {% load static %}
    {% load widget_tweaks %}

    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@400;500;600;700;800&family=Playfair+Display:wght@400;500;600;700&display=swap" rel="stylesheet">

    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">

    <style>
        /* CozyWish Design System - Notifications App */
        /* Following design_reference standards with professional brand styling */

        /* CSS Custom Properties */
        :root {
            /* Brand Colors */
            --cw-brand-primary: #2F160F;
            --cw-brand-light: #4a2a1f;
            --cw-brand-accent: #fae1d7;
            --cw-accent-light: #fef7f0;
            --cw-accent-dark: #f1d4c4;

            /* Secondary Colors */
            --cw-secondary-50: #f9f7f4;
            --cw-secondary-100: #f1ebe2;
            --cw-secondary-200: #e3d5c4;
            --cw-secondary-300: #d1b89e;
            --cw-secondary-400: #bc9876;
            --cw-secondary-500: #ad7f5a;
            --cw-secondary-600: #a0704e;
            --cw-secondary-700: #855a42;
            --cw-secondary-800: #6c4a39;
            --cw-secondary-900: #583d30;
            --cw-secondary-950: #2f1f18;

            /* Neutral Colors */
            --cw-neutral-50: #fafafa;
            --cw-neutral-100: #f5f5f5;
            --cw-neutral-200: #e5e5e5;
            --cw-neutral-300: #d4d4d4;
            --cw-neutral-400: #a3a3a3;
            --cw-neutral-500: #737373;
            --cw-neutral-600: #525252;
            --cw-neutral-700: #404040;
            --cw-neutral-800: #262626;
            --cw-neutral-900: #171717;
            --cw-neutral-950: #0a0a0a;

            /* Semantic Colors */
            --cw-success: #059669;
            --cw-warning: #d97706;
            --cw-error: #dc2626;
            --cw-info: #0284c7;

            /* Typography */
            --cw-font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            --cw-font-heading: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            --cw-font-display: 'Playfair Display', Georgia, 'Times New Roman', serif;

            /* Shadows */
            --cw-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
            --cw-shadow-base: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
            --cw-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            --cw-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            --cw-shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

            /* Gradients */
            --cw-gradient-hero: radial-gradient(ellipse at center, var(--cw-brand-accent) 40%, var(--cw-accent-light) 70%, #ffffff 100%);
            --cw-gradient-hero-alt: radial-gradient(ellipse at center, var(--cw-accent-light) 0%, var(--cw-accent-dark) 40%, #ffffff 100%);
            --cw-gradient-card: linear-gradient(135deg, #ffffff 0%, var(--cw-brand-accent) 100%);
            --cw-gradient-card-subtle: linear-gradient(135deg, #ffffff 0%, var(--cw-accent-light) 100%);
            --cw-gradient-button: linear-gradient(135deg, var(--cw-brand-primary) 0%, var(--cw-brand-light) 100%);
            --cw-gradient-brand-button: linear-gradient(135deg, var(--cw-brand-primary) 0%, var(--cw-brand-light) 100%);
            --cw-gradient-accent: linear-gradient(135deg, var(--cw-brand-accent) 0%, var(--cw-accent-dark) 100%);
        }

        /* Global Styles */
        body {
            font-family: var(--cw-font-primary);
            line-height: 1.6;
            color: var(--cw-neutral-800);
        }

        /* Typography */
        h1, h2, h3, h4, h5, h6 {
            font-family: var(--cw-font-heading);
            font-weight: 600;
            color: var(--cw-secondary-950);
            line-height: 1.3;
        }

        .display-font {
            font-family: var(--cw-font-display);
        }

        /* Brand Colors */
        .text-brand-cw { color: var(--cw-brand-primary) !important; }
        .text-brand-light-cw { color: var(--cw-brand-light) !important; }
        .text-accent-cw { color: var(--cw-brand-accent) !important; }
        .text-secondary-cw { color: var(--cw-secondary-950) !important; }
        .text-neutral-cw { color: var(--cw-neutral-600) !important; }
        .bg-brand-cw { background-color: var(--cw-brand-primary) !important; }
        .bg-brand-accent-cw { background-color: var(--cw-brand-accent) !important; }
        .bg-accent-dark-cw { background-color: var(--cw-accent-dark) !important; }
        .bg-light-cw { background-color: var(--cw-accent-light) !important; }

        /* Notifications wrapper */
        .notifications-wrapper {
            background: var(--cw-gradient-hero);
            min-height: 100vh;
            padding: 2rem 0;
            font-family: var(--cw-font-primary);
        }

        /* Custom Buttons */
        .btn-cw-primary {
            background: var(--cw-gradient-brand-button);
            border: none;
            border-radius: 0.5rem;
            font-weight: 600;
            padding: 0.75rem 1.5rem;
            color: white;
            transition: all 0.2s ease;
            box-shadow: var(--cw-shadow-sm);
        }
        .btn-cw-primary:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(47, 22, 15, 0.3);
            color: white;
        }

        .btn-cw-brand {
            background: var(--cw-gradient-brand-button);
            border: none;
            border-radius: 0.5rem;
            font-weight: 600;
            padding: 0.75rem 1.5rem;
            color: white;
            transition: all 0.2s ease;
            box-shadow: var(--cw-shadow-sm);
        }
        .btn-cw-brand:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(47, 22, 15, 0.3);
            color: white;
        }

        .btn-cw-secondary {
            border: 2px solid var(--cw-brand-primary);
            color: var(--cw-brand-primary);
            border-radius: 0.5rem;
            font-weight: 600;
            padding: 0.75rem 1.5rem;
            background: white;
            transition: all 0.2s ease;
        }
        .btn-cw-secondary:hover {
            background: var(--cw-brand-primary);
            color: white;
            transform: translateY(-1px);
        }

        .btn-cw-accent {
            background: var(--cw-gradient-accent);
            border: none;
            border-radius: 0.5rem;
            font-weight: 600;
            padding: 0.75rem 1.5rem;
            color: var(--cw-brand-primary);
            transition: all 0.2s ease;
            box-shadow: var(--cw-shadow-sm);
        }
        .btn-cw-accent:hover {
            transform: translateY(-1px);
            box-shadow: var(--cw-shadow-md);
            color: var(--cw-brand-primary);
        }

        .btn-cw-ghost {
            color: var(--cw-brand-primary);
            text-decoration: none;
            font-weight: 500;
            padding: 0.5rem 1rem;
            border: none;
            background: transparent;
            transition: all 0.2s ease;
        }
        .btn-cw-ghost:hover {
            color: var(--cw-brand-light);
            text-decoration: underline;
        }

        /* Custom Cards */
        .card-cw {
            border: 1px solid var(--cw-neutral-200);
            border-radius: 1rem;
            box-shadow: var(--cw-shadow-md);
            background: white;
            overflow: hidden;
            transition: all 0.3s ease;
        }
        .card-cw:hover {
            transform: translateY(-2px);
            box-shadow: var(--cw-shadow-lg);
        }

        .card-cw-featured {
            border: 2px solid var(--cw-brand-primary);
            background: var(--cw-gradient-card-subtle);
        }

        .card-cw-brand {
            border: 2px solid var(--cw-brand-primary);
            background: var(--cw-gradient-card);
            box-shadow: 0 10px 15px -3px rgba(47, 22, 15, 0.1);
        }

        .card-cw-accent {
            border: 1px solid var(--cw-brand-accent);
            background: var(--cw-brand-accent);
            box-shadow: var(--cw-shadow-sm);
        }

        /* Custom Forms */
        .form-control-cw {
            border: 2px solid var(--cw-neutral-200);
            border-radius: 0.5rem;
            padding: 0.75rem 1rem;
            font-size: 1rem;
            transition: all 0.2s ease;
        }
        .form-control-cw:focus {
            border-color: var(--cw-brand-primary);
            box-shadow: 0 0 0 0.2rem rgba(47, 22, 15, 0.1);
        }

        /* Badges */
        .badge-cw-primary {
            background: var(--cw-brand-primary);
            color: white;
            font-weight: 500;
            padding: 0.25rem 0.75rem;
            border-radius: 9999px;
        }

        .badge-cw-secondary {
            background: var(--cw-neutral-100);
            color: var(--cw-neutral-700);
            font-weight: 500;
            padding: 0.25rem 0.75rem;
            border-radius: 9999px;
        }

        /* Alerts */
        .alert-cw-success {
            background: #f0fdf4;
            border: 1px solid #bbf7d0;
            color: #166534;
            border-radius: 0.5rem;
        }

        .alert-cw-warning {
            background: #fffbeb;
            border: 1px solid #fed7aa;
            color: #92400e;
            border-radius: 0.5rem;
        }

        .alert-cw-error {
            background: #fef2f2;
            border: 1px solid #fecaca;
            color: #991b1b;
            border-radius: 0.5rem;
        }

        /* Utility Classes */
        .shadow-cw-sm { box-shadow: var(--cw-shadow-sm); }
        .shadow-cw-md { box-shadow: var(--cw-shadow-md); }
        .shadow-cw-lg { box-shadow: var(--cw-shadow-lg); }

        .rounded-cw { border-radius: 0.5rem; }
        .rounded-cw-lg { border-radius: 1rem; }
        .rounded-cw-full { border-radius: 9999px; }

        /* Notification specific styling */
        .notification-item {
            border: 1px solid var(--cw-neutral-200);
            border-radius: 1rem;
            padding: 1.5rem;
            margin-bottom: 1rem;
            background-color: white;
            transition: all 0.3s ease;
            box-shadow: var(--cw-shadow-sm);
        }

        .notification-item:hover {
            transform: translateY(-2px);
            box-shadow: var(--cw-shadow-md);
        }

        .notification-item.unread {
            border-color: var(--cw-brand-primary);
            background: var(--cw-gradient-card-subtle);
        }

        .notification-item.read {
            border-color: var(--cw-neutral-200);
            background-color: white;
        }

        .notification-icon {
            background: var(--cw-gradient-accent);
            border: 2px solid var(--cw-brand-primary);
            border-radius: 50%;
            width: 48px;
            height: 48px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--cw-brand-primary);
            box-shadow: var(--cw-shadow-sm);
        }

        .notification-time {
            font-size: 0.875rem;
            color: var(--cw-neutral-600);
            font-family: var(--cw-font-primary);
        }

        /* Navigation */
        .navbar-cw {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            box-shadow: var(--cw-shadow-sm);
        }

        .navbar-brand-cw {
            font-family: var(--cw-font-heading);
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--cw-brand-primary);
        }

        .nav-link-cw {
            font-weight: 500;
            color: var(--cw-neutral-700);
            padding: 0.5rem 1rem;
            transition: color 0.2s ease;
        }
        .nav-link-cw:hover {
            color: var(--cw-brand-primary);
        }

        /* Breadcrumb styling */
        .breadcrumb {
            background: var(--cw-gradient-card-subtle);
            border: 1px solid var(--cw-neutral-200);
            border-radius: 0.5rem;
            padding: 0.75rem 1rem;
        }

        .breadcrumb-item a {
            color: var(--cw-brand-primary);
            text-decoration: none;
            font-weight: 500;
        }

        .breadcrumb-item a:hover {
            color: var(--cw-brand-light);
        }

        .breadcrumb-item.active {
            color: var(--cw-neutral-600);
        }

        /* Responsive design */
        @media (max-width: 768px) {
            .notifications-wrapper {
                padding: 1rem 0;
            }

            .notification-item {
                padding: 1rem;
                margin-bottom: 0.75rem;
            }

            .notification-icon {
                width: 40px;
                height: 40px;
            }
        }
    </style>
    {% block notifications_extra_css %}{% endblock %}
{% endblock %}

{% block content %}
<div class="notifications-wrapper">
    <div class="container py-4" style="max-width: 800px;">
        {% block notifications_content %}{% endblock %}
    </div>
</div>
{% endblock %}

{% block extra_js %}
    {% block notifications_extra_js %}{% endblock %}
{% endblock %}
